{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:mobile": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.10", "@tailwindcss/vite": "^4.1.8", "@turf/turf": "^7.2.0", "gyronorm": "^2.0.6", "ol": "^10.5.0", "ol-ext": "^4.0.31", "ol-mapbox-style": "^13.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "supabase": "^2.24.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@vitejs/plugin-react": "^4.4.1", "daisyui": "^5.0.43", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0"}}