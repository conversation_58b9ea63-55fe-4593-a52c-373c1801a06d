@import url("https://fonts.googleapis.com/css2?family=Madimi+One&display=swap");
@import "tailwindcss";
@import "ol/ol.css";
@plugin "daisyui";
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-white: #f4f4f4;
  --color-green: #50aa61;
  --color-yellow: #f3c549;
  --color-black: #121212;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body,
#root {
  height: 100%;
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height */
  height: 100svh; /* Small viewport height */
}

/* --- Global Styles --- */
body {
  font-family: "Madimi One", serif;
  font-weight: 400;
  font-style: normal;
  line-height: 1.5;
  letter-spacing: 1px !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Feedback visuel de précision */
.position-feedback {
  position: absolute;
  bottom: 80px;
  left: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 10px;
  z-index: 1000;
}

.accuracy-meter {
  height: 10px;
  background: #f3f4f6;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 8px;
}

.accuracy-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  transition: width 0.5s ease;
  position: relative;
}

.accuracy-bar::after {
  content: attr(data-accuracy);
  position: absolute;
  right: 5px;
  top: -20px;
  font-size: 12px;
  color: #374151;
}

.gps-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #064e3b;
}

.gps-status.weak {
  color: #991b1b;
}

.gps-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

.gps-status.weak .gps-indicator {
  background: #ef4444;
}

/* Styles des sources de position */
.position-marker[data-source="gps"] {
  background-color: #34a853; /* Vert - Position précise */
}
.position-marker[data-source="network"] {
  background-color: #fbbc05; /* Jaune - Position approximative */
}
.position-marker[data-source="fallback"] {
  background-color: #ea4335; /* Rouge - Position par défaut */
}
.position-marker[data-source="debug"] {
  background-color: #4285f4; /* Bleu - Mode debug */
}
.position-marker[data-source="google"] {
  background-color: #34a853;
}
.position-marker[data-source="network"] {
  background-color: #fbbc05;
}
.position-marker[data-source="default"] {
  background-color: #ea4335;
}
.position-marker[data-source="debug"] {
  background-color: #4285f4;
}

/* Header Styles */
.header {
  height: 60px;
  background: linear-gradient(135deg, var(--color-green), var(--color-yellow));
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

/* Footer Styles */
.footer {
  height: 60px;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

/* --- Map Container & Map --- */
.map {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 60px;
  width: 100%;
  height: 100%;
  z-index: 1;
  outline: none;
  background-color: #e9ecef;
}

/* Pour la modal */
.fixed.inset-0.z-50 {
  background-color: transparent;
  pointer-events: none;
}

/* Rétablir les interactions sur le contenu de la modal */
.fixed.inset-0.z-50 > div {
  pointer-events: auto;
}

/* --- Recenter Button --- */
.recenter-button {
  position: absolute;
  bottom: 85px;
  left: 25px;
  background-color: #ffffff;
  color: #007bff; /* Primary blue */
  border: none;
  border-radius: 50%; /* Circular button */
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px; /* Adjust icon size */
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 1000; /* Ensure it's above the map controls */
}

.recenter-button:hover {
  background-color: #f1f3f5;
  color: #0056b3; /* Darker blue on hover */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.recenter-button:active {
  background-color: #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

/* --- Modal Styles --- */
/* General modal backdrop animation (Tailwind's .fixed.inset-0.bg-black) */
.fixed.inset-0.bg-black {
  animation: fadeInModal 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.welcome-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(18, 18, 18, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(8px);
  animation: fadeInModal 0.4s ease-out;
}

.welcome-modal {
  width: 90%;
  max-width: 400px;
  background-color: var(--color-white);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform-origin: center;
  animation: slideInModal 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

.modal-header {
  padding: 2rem 2rem 1.5rem;
  background: linear-gradient(135deg, var(--color-green), var(--color-yellow));
  color: var(--color-white);
  text-align: center;
}

.modal-header h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.modal-header p {
  font-size: 0.9rem;
  opacity: 0.9;
}

.modal-form {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  /* justify-content: center;
  align-items: center; */
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--color-black);
  opacity: 0.8;
  background-color: var(--color-white);
}

.form-group span {
  background-color: var(--color-white);
}

.form-group input {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 2px solid rgba(18, 18, 18, 0.1);
  border-radius: 10px;
  font-family: "Madimi One", serif;
  font-size: 1rem;
  background-color: var(--color-white);
  color: var(--color-black);
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--color-green);
  background-color: var(--color-white);
  box-shadow: 0 0 0 3px rgba(80, 170, 97, 0.2);
}

.submit-btn {
  width: auto;
  padding: 1rem;
  margin-inline: auto;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--color-yellow), var(--color-green));
  color: var(--color-white);
  font-family: "Madimi One", serif;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submit-btn:hover {
  background: #429c55;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.go-bike {
  display: inline-block;
  transform: scaleX(-1); /* Effet miroir horizontal */
  font-size: 2rem;
}

/* Add this class to the main content div of ArrivalModal */
/* e.g., <div className="bg-white rounded-xl p-6 max-w-md w-full shadow-xl arrival-modal-content"> */
.arrival-modal-content {
  /* Tailwind's shadow-xl is good. This enhances the border-radius. */
  border-radius: 0.75rem; /* Tailwind's rounded-xl is 0.75rem, consistent */
  animation: slideInUpModal 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Add class="icon-success" to the SVG in ArrivalModal */
.arrival-modal-content .icon-success {
  /* Tailwind: h-16 w-16 mx-auto text-green-500 mb-4 animate-bounce */
  color: #28a745; /* A vibrant success green */
}

.arrival-modal-content h2 {
  /* Tailwind: text-2xl font-bold text-gray-800 mb-2 */
  color: #1f2937; /* Tailwind gray-800 */
  font-weight: 700;
}

/* Add class="destination-info-box" to the div with bg-blue-50 in ArrivalModal */
.arrival-modal-content .destination-info-box {
  /* Tailwind: bg-blue-50 rounded-lg p-4 mb-4 */
  background-color: #e7f3ff; /* Lighter, more subtle blue */
  border-left: 5px solid #007bff; /* Primary blue accent */
  padding: 1rem 1.25rem;
  border-radius: 0.5rem; /* Tailwind's rounded-lg */
}

.arrival-modal-content .destination-info-box p {
  /* Tailwind: text-lg font-medium text-blue-800 */
  color: #0056b3; /* Darker blue for better readability */
  font-size: 1rem; /* Tailwind's text-lg is 1.125rem, adjusted for consistency */
}

.arrival-modal-content .destination-info-box .font-bold {
  color: #004085; /* Even darker for the bolded part */
}

/* Add class="action-button" to both buttons in ArrivalModal, */
/* and "primary" or "secondary" for specific styling */
/* e.g., <button className="... action-button primary"> */
/* e.g., <button className="... action-button secondary"> */
.arrival-modal-content .action-button {
  /* Tailwind: py-3 px-4 rounded-xl font-medium transition-colors */
  padding: 0.8rem 1.5rem; /* Adjusted padding */
  border-radius: 0.5rem; /* Tailwind rounded-xl */
  font-weight: 500;
  transition: background-color 0.2s ease, transform 0.15s ease,
    box-shadow 0.15s ease;
  border: none;
  text-transform: capitalize;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

.arrival-modal-content .action-button svg {
  /* Tailwind: h-5 w-5 mr-2 */
  margin-right: 0.5rem; /* Adjusted margin */
}

.arrival-modal-content .action-button.primary {
  /* Tailwind: bg-blue-600 hover:bg-blue-700 text-white */
  background-color: #007bff;
  color: white;
}
.arrival-modal-content .action-button.primary:hover {
  background-color: #0069d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.arrival-modal-content .action-button.secondary {
  /* Tailwind: bg-gray-600 hover:bg-gray-700 text-white */
  background-color: #6c757d; /* Bootstrap secondary gray */
  color: white;
}
.arrival-modal-content .action-button.secondary:hover {
  background-color: #5a6268; /* Darker gray */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.25);
}

.arrival-modal-content .action-button:active {
  transform: translateY(0);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* --- Keyframe Animations --- */
@keyframes fadeInModal {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUpModal {
  from {
    transform: translateY(40px) scale(0.98);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* --- OpenLayers Control Button Styling (Optional) --- */
/* This makes OL default controls (like zoom) fit the theme a bit better */
.ol-control button {
  background-color: rgba(255, 255, 255, 0.85) !important;
  color: #007bff !important;
  border-radius: 0.375rem !important; /* rounded-md */
  margin: 2px !important;
  transition: background-color 0.2s ease;
}

.ol-control button:hover {
  background-color: rgba(240, 240, 240, 0.95) !important;
}

.ol-zoom {
  top: 20px;
  left: auto;
  right: 20px;
  bottom: auto;
  background-color: transparent !important;
}

.ol-attribution {
  background: rgba(255, 255, 255, 0.7) !important;
  border-radius: 4px 0 0 0 !important;
  padding: 2px 6px !important;
}
.ol-attribution ul {
  font-size: 0.7rem !important;
  color: #333 !important;
}
.ol-attribution a {
  color: #007bff !important;
}

/* --- Navigation Controls --- */
.navigation-controls {
  position: absolute;
  bottom: 85px;
  right: 25px;
  z-index: 1000;
}

.navigation-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 25px;
  font-family: "Madimi One", serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  justify-content: center;
}

.start-navigation {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.start-navigation:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.stop-navigation {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.stop-navigation:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.navigation-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}

.distance-info {
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 12px;
  color: #374151;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
  min-width: 100px;
}

.distance-info span {
  font-weight: 500;
}

.route-provider {
  font-size: 10px !important;
  opacity: 0.7;
  font-style: italic;
}

/* Animation pour les boutons de navigation */
.navigation-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Optimisations pour smartphones (PWA mobile uniquement) */
/* Ajustements pour petits écrans de smartphones */
@media (max-width: 480px) {
  .navigation-controls {
    bottom: 75px;
    right: 15px;
  }

  .navigation-button {
    padding: 10px 14px;
    font-size: 13px;
    min-width: 100px;
  }

  .distance-info {
    font-size: 11px;
    padding: 6px 10px;
  }

  .position-info {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Optimisations pour très petits smartphones */
@media (max-width: 360px) {
  .navigation-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 90px;
  }

  .position-info {
    font-size: 10px;
    padding: 3px 6px;
  }
}
